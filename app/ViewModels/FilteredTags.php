<?php
 
namespace App\ViewModels;
 
use Statamic\View\ViewModel;
use Statamic\Facades\Entry;
 
class FilteredTags extends ViewModel
{
    public function data(): array
    {
		$filtered_tags = [];

		// Use the query builder to fetch entries from the 'perspectives' collection
        $entries = Entry::query()
            ->where('collection', 'case_studies') // Replace with your collection handle
			->whereTaxonomy('case_study_categories::creative')
            ->get();

		foreach($entries as $entry) {
			foreach($entry->tags as $tag) {
				// Check if the item is not already in the array
				if (!in_array($tag, $filtered_tags)) {
					// Add the item to the array
					$filtered_tags[] = $tag;
				}
			}
		}

		// dd($filtered_tags);

        return [
            'filtered_tags' => $filtered_tags
        ];
    }
}
