{{#
    @name Case Studies show
    @desc The Case Studies show template.
#}}

{{?
	$breadcrumbs = [
		[
			'title' => 'Our Work',
			'breadcrumb_url' => '/our-work'
		],
		[
			'title' => $title
		]
	];
?}}

<!-- /case_studies/show.antlers.html -->
<div class="content-area-a js-slider-over">
	<section class="relative bg-{{ colour_scheme }}-accent pt-12 pb-7">
		{{ partial:layout/header class="top-0 left-0 right-0 z-20 absolute" bg="bg-transparent" breadcrumbs="true" breadcrumbs="{{ breadcrumbs }}" bread_crumb_hover_color="hover:text-{{ colour_scheme }}-accent" }}

		<div class="container px-4 py-14">
			<div class="grid grid-cols-12">
				<div class="col-span-10 col-start-2">
					<h1 class="
						text-white 
						text-24
						font-bold
					">{{ title }}</h1>
				</div>
			</div>
			<div class="grid grid-cols-12 mb-7">
				<div class="col-span-1 text-right">
					
					<div class="
						inline-block
						w-[16px]
						h-[94px]
						lg:w-5
						lg:h-14
						border-[3px]
						lg:border-4
						border-white
						-skew-x-[15deg]
						translate-y-3
						lg:translate-y-4
						mr-10
					"></div>
				</div>
				<div class="col-span-10 lg:col-span-8">
					<div class="
						text-white 
						text-48
						sm:text-56
						lg:text-112 
						font-bold
					">{{ hero_title }}</div>
				</div>
			</div>
			<div class="grid grid-cols-12">
				<div class="col-span-10 col-start-2 border-t border-white pt-6 text-white grid grid-cols-12">
					<div class="col-span-12 lg:col-span-6 lg:grid lg:grid-cols-2 gap-6 mb-6 lg:mb-0">
						<div class="mb-6 lg:mb-0">
							<div class="text-18 font-semibold mb-3">Industry</div>
							{{ industry }}
								<p>{{ value }}</p>
							{{ /industry }}
						</div>
						<div>
							<div class="text-18 font-semibold mb-3">Services</div>
							<div class="flex flex-wrap gap-4">
								{{ tags }}
									<div>{{ partial:atoms/tag url="/our-work/tags/{{slug}}/" theme="dark" small="true" :label="title" }}</div>
								{{ /tags }}
							</div>
						</div>
					</div>
					<div class="col-span-12 lg:col-span-2 lg:col-start-11">
						<div class="text-18 font-semibold mb-3">Contributors</div>
						{{ contributers }}
							<p>{{ value }}</p>
						{{ /contributers }}
					</div>
				</div>
			</div>
		</div>
	</section>
</div>

<div class="content-area-b relative z-20">
	<div class="bg-white rounded-t-lg pt-10 lg:pt-12 -mt-7 z-20 relative">
		<section class="">
			<div class="container px-4">
				<div class="grid grid-cols-12">
					<div class="col-span-10 col-start-2">
						
						<div x-data="{ 
							modalOpen: true,
							calculateSize() {
								const container = this.$refs.container;
								const aspectRatio = 1744 / 997;
					
								// Calculate maximum possible width and height
								const maxWidth = Math.min(window.innerWidth * 0.9, 1200);
								const maxHeight = window.innerHeight * 0.9;
					
								// Determine which dimension is the limiting factor
								const heightFromWidth = maxWidth / aspectRatio;
								const widthFromHeight = maxHeight * aspectRatio;
					
								if (heightFromWidth <= maxHeight) {
									// Width is the limiting factor
									container.style.width = `${maxWidth}px`;
									container.style.height = `${heightFromWidth}px`;
								} else {
									// Height is the limiting factor
									container.style.width = `${widthFromHeight}px`;
									container.style.height = `${maxHeight}px`;
								}
							}
						}"
						x-init="$nextTick(() => { 
							calculateSize(); 
							document.getElementById('modal').showModal(); 
						})"
					>

						<button 
							@click="modalOpen = true; $nextTick(() => { calculateSize(); document.getElementById('modal').showModal(); })"
							class="
								c-button
								c-button--line
								font-14 
								font-semibold
								uppercase
								text-white 
								bg-{{ colour_scheme }}-accent
								hover:text-dark
								hover:bg-transparent
								hover:border-dark
								border
								border-transparent
								rounded-sm 
								focus:outline-none 
								focus-visible:ring 
								focus-visible:ring-offset-2 
								inline-flex 
								items-center 
								px-4 py-3
								leading-none 
								no-underline 
								select-none 
								whitespace-nowrap 
								motion-safe:transition
								group/button
								mb-10
							"
							id="open-modal"
						>
							<span class="px-2">View Case Study</span>
						</button>
					
						<!-- Modal -->
						<dialog 
							id="modal" 
							x-show="modalOpen"
							x-transition.opacity
							class="fixed inset-0 flex items-center justify-center bg-black/50 p-4 m-0 border-none outline-none w-full h-full"
							@click="modalOpen = false; $el.close();"
						>
							<div 
								class="relative bg-white overflow-hidden w-full md:w-[70%] max-w-[1200px] aspect-[1744/1020] flex items-center justify-center" 
								@click.stop
							>
								<!-- Close button -->
								<button 
									class="absolute top-4 right-4 p-2 text-black hover:text-red-600 focus:outline-none z-50 transition-all duration-300"
									@click="modalOpen = false; document.getElementById('modal').close();"
								>
									<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
									</svg>
								</button>

								<!-- Iframe content -->
								<iframe 
									class="w-full h-full border-none" 
									src="{{ google_slide_embed_url }}"
									allowfullscreen="true" 
									mozallowfullscreen="true" 
									webkitallowfullscreen="true"
								></iframe>
							</div>
						</dialog>
						</div>
					
						<div class="grid grid-cols-12 items-center mb-12">
							<div class="col-span-12 lg:col-span-5">
								{{ partial src="page_builder/article" }}
							</div>
							<div class="col-span-12 lg:col-span-5 lg:col-start-7">
								<article class="
									prose
									prose-a:no-underline
									prose-a:decoration-primary
									hover:prose-a:text-primary
									focus:prose-a:outline-none
									focus-visible:prose-a:ring
									focus-visible:prose-a:ring-primary
									focus-visible:prose-a:rounded-sm
									prose-figure:my-0
									prose-pre:whitespace-pre-wrap
									prose-strong:text-24
									max-w-none
									prose-h2:text-32
									prose-h2:text-dark
									prose-h2:font-bold
									prose-h2:mb-3
									prose-hr:my-6"
								>
								{{ right_column_article }}
									{{ partial src="components/{type}" }}
								{{ /right_column_article }}
								</article>								
							</div>
						</div>
		
						<div class="grid grid-cols-12 items-end mb-14">
							{{ if testimonial }}
								<div class="col-span-12 lg:col-span-5 mb-6 lg:mb-0">
									<div class="text-36 font-bold mb-3">"{{ testimonial }}"</div>
									{{ if testimonial_author }}
										<div class="text-grey-800">- {{ testimonial_author }}</div>
									{{ /if }}
								</div>
							{{ /if }}
							{{ if external_links }}
								<div class="col-span-12 lg:col-span-5 lg:col-start-8">
									<ul class="border-t border-grey-800">
										{{ external_links }}
											<li class="py-6 flex justify-between items-center border-b border-grey-800">
												<div>
													<div class="text-24 font-bold">{{ title }}</div>
												</div>
												<div>
													{{ partial:components/button custom_attributes="target='_blank'" :url="url" type="line" right_icon="link"}}
												</div>
											</li>
										{{ /external_links }}
									</ul>
								</div>
							{{ /if }}
						</div>
					</div>
				</div>
			</div>
		</section>
		
		{{ page_builder scope="block" }}
			{{ partial src="page_builder/{type}" }}
		{{ /page_builder }}
		
		{{ if related_case_study }}
			{{ related_case_study }}
				{{ partial:components/case_study_feature }}
			{{ /related_case_study }}
		{{ /if }}
		{{ partial:layout/footer }}
	</div>
</div>
<!-- /case_studies/show.antlers.html -->
