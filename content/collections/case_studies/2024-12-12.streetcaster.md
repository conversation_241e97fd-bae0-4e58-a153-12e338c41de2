---
id: 942b2b85-7328-4b2b-9b26-bbde8dac763b
blueprint: case_studies
title: Streetcaster
hero_title: 'Innovation in the film industry.'
brand_description: |-
  A portal for film production teams to easily find acting talent for their next project and for that talent to get found - all from the palm of their hands, and out on the streets! 
  Put out a brief as a client member and notify all talent members who match the criteria that their next big role is out. Upload a casting tape right then and there, now hold thumbs for the callback.
industry:
  - Film
  - Entertainment
contributers:
  - '<PERSON><PERSON>'
  - '<PERSON>'
google_slide_embed_url: 'https://docs.google.com/presentation/d/e/2PACX-1vQx3dAKwr230cE1x5_QmnhxqouuP5ZBbv3m1E345UrF6u3jeFnVtnvwTNuCt4uhsCv1yQNS_Nd3W8yD/embed?start=false&loop=false&delayms=3000'
article:
  -
    type: heading
    attrs:
      level: 2
    content:
      -
        type: text
        text: 'Who is Streetcaster?'
  -
    type: paragraph
    content:
      -
        type: text
        text: "Streetcaster is a portal for film production teams to easily find acting talent for their next project and for that talent to get found - all from the palm of their hands, and out on the streets!\_"
  -
    type: paragraph
    content:
      -
        type: text
        text: 'Put out a brief as a client member and notify all talent members who match the criteria that their next big role is out. Upload a casting tape right then and there, now hold thumbs for the callback.'
  -
    type: set
    attrs:
      id: m4l3209u
      values:
        type: seperator
        dark_theme: false
  -
    type: heading
    attrs:
      level: 2
    content:
      -
        type: text
        text: 'What was the ask?'
  -
    type: paragraph
    content:
      -
        type: text
        text: "When the Filmer.tv team (a film production crew we built another platform for) came to us, they had a name and a want to make it easier to find the cast for their client’s projects.\_"
  -
    type: paragraph
    content:
      -
        type: text
        text: "We saw an opportunity to completely change the way the industry would go about casting in the future: Streetcaster - Find cast / Get cast.\_"
  -
    type: paragraph
    content:
      -
        type: text
        text: "We strategised a way to take this idea to another level of ease and execution for BOTH sides of the conversation by creating a PWA (essentially a mobile application) that would enable the parties' first interactions anytime, anywhere and all in one place.\_"
  -
    type: paragraph
    content:
      -
        type: text
        text: 'Clients put out a brief on the app with the details of what cast they are looking for and key criteria are matched to acting talent who are then able to submit a casting video right in the app and add a few extra pieces of info to help their cause. When the casting timeline is over, the client can review all the submissions and headshots and then choose to reject or accept their perfect cast.'
right_column_article:
  -
    type: heading
    attrs:
      level: 2
    content:
      -
        type: text
        text: 'How we delivered:'
  -
    type: paragraph
    content:
      -
        type: text
        text: "A few of the key elements:\_"
  -
    type: paragraph
    content:
      -
        type: text
        text: "Full brief customisation - get exactly what you’re looking for as a client.\_"
  -
    type: paragraph
    content:
      -
        type: text
        text: "Direct phone video uploads - seamless submissions without skipping a beat.\_"
  -
    type: paragraph
    content:
      -
        type: text
        text: 'Brief suggestion function - not right for the brief? Share it with someone who is (and grow our members)'
  -
    type: paragraph
    content:
      -
        type: text
        text: "Complete sync - mobile, desktop, multiple users, anytime, anywhere, always-on.\_"
  -
    type: paragraph
    content:
      -
        type: text
        text: "Due to timeline and budget constraints, a vital part of our approach to this mammoth undertaking was the workflow. While we identified a clear opportunity in the market, there are many factors that go into something like this, so we went into the partnership with the Streetcaster crew in a prototype approach.\_"
  -
    type: paragraph
    content:
      -
        type: text
        text: 'What we have created so far is a basic brand and MVP to test the audience - no frills and no fuss, only the base functionality for now as we get user data to inform the next update and make it even easier to Find cast / Get cast.'
external_links:
  -
    id: m4l33h9a
    title: 'Ready for your 15 minutes?'
    url: 'http://www.streetcaster.tv'
seo_title: Streetcaster
seo_description: 'A portal for film production teams to easily find acting talent for their next project and for that talent to get found.'
seo_noindex: false
seo_nofollow: false
seo_canonical_type: entry
sitemap_change_frequency: weekly
sitemap_priority: 0.5
use_dark_header_theme: false
colour_scheme: streetcaster
tags:
  - development
  - strategy
  - progressive-web-apps
  - ui-design
  - ux-design
  - pwa
feature_on_home_page: false
case_study_categories:
  - development
  - strategy
feature_image:
  src: case-study-covers/responsive/streetcaster-updated-small.jpg
  ratio: '0.75'
  'sm:src': case-study-covers/responsive/streetcaster-updated-small.jpg
  'sm:ratio': '0.75'
  'sm:glide:fit': crop_focal
  'lg:src': case-study-covers/responsive/streetcaster-updated-large.jpg
  'lg:ratio': '3'
  'lg:glide:fit': crop_focal
listing_page_image:
  src: case-study-covers/responsive/streetcaster-updated-small.jpg
  ratio: '1.1'
  'glide:fit': crop_focal
updated_by: f091bb75-d170-4c39-ba1b-a16dbaf89422
updated_at: 1747312150
og_image: casestudy_streetcaster_li.jpg
---
